interface SensorReading {
  value: string | number; // Allow both string and number for flexibility
  timestamp: string;
  unit?: string; // Made optional to maintain backward compatibility
}

interface SensorData {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'unknown'; // Added 'unknown' status
  batteryLevel: number;
  location: { lat: number; long: number };
  readings: {
    salinity: SensorReading;
    do: SensorReading;
    ph: SensorReading;
    temp: SensorReading;
    cond: SensorReading;
    depth: SensorReading;
    turbidity: SensorReading;
  };
}

export const sensorsData: Record<string, SensorData> = {
  'NB001': {
    id: 'NB001',
    name: 'Sensor 1',
    status: 'online',
    batteryLevel: 79,
    location: { lat: 24.6919, long: 54.5415 },
    readings: {
      salinity: { value: '42.67', unit: 'ppt', timestamp: '21/10/2024 0:15' },
      do: { value: '6.53', unit: 'mg/L', timestamp: '21/10/2024 0:15' },
      ph: { value: '8.24', unit: 'pH', timestamp: '21/10/2024 0:15' },
      temp: { value: '31.80', unit: '°C', timestamp: '21/10/2024 0:15' },
      cond: { value: '62.79', unit: 'mS/cm', timestamp: '21/10/2024 0:15' },
      depth: { value: '0.24', unit: 'm', timestamp: '21/10/2024 0:15' },
      turbidity: { value: '5.73', unit: 'NTU', timestamp: '21/10/2024 0:15' }
    }
  },
  'NB002': {
    id: 'NB002',
    name: 'Sensor 2',
    status: 'offline',
    batteryLevel: 0,
    location: { lat: 24.7898, long: 54.2153 },
    readings: {
      salinity: { value: '0', unit: 'ppt', timestamp: 'No data' },
      do: { value: '0', unit: 'mg/L', timestamp: 'No data' },
      ph: { value: '0', unit: 'pH', timestamp: 'No data' },
      temp: { value: '0', unit: '°C', timestamp: 'No data' },
      cond: { value: '0', unit: 'mS/cm', timestamp: 'No data' },
      depth: { value: '0', unit: 'm', timestamp: 'No data' },
      turbidity: { value: '0', unit: 'NTU', timestamp: 'No data' }
    }
  },
  'NB003': {
    id: 'NB003',
    name: 'Sensor 3',
    status: 'online',
    batteryLevel: 92,
    location: { lat: 24.8010, long: 54.6715 },
    readings: {
      salinity: { value: '41.23', unit: 'ppt', timestamp: '21/10/2024 0:15' },
      do: { value: '6.78', unit: 'mg/L', timestamp: '21/10/2024 0:15' },
      ph: { value: '8.12', unit: 'pH', timestamp: '21/10/2024 0:15' },
      temp: { value: '30.95', unit: '°C', timestamp: '21/10/2024 0:15' },
      cond: { value: '61.45', unit: 'mS/cm', timestamp: '21/10/2024 0:15' },
      depth: { value: '0.32', unit: 'm', timestamp: '21/10/2024 0:15' },
      turbidity: { value: '4.89', unit: 'NTU', timestamp: '21/10/2024 0:15' }
    }
  },
  // Additional fallback sensors for better coverage
  'NB004': {
    id: 'NB004',
    name: 'Sensor 4',
    status: 'online',
    batteryLevel: 88,
    location: { lat: 24.7500, long: 54.6000 },
    readings: {
      salinity: { value: '40.15', unit: 'ppt', timestamp: '21/10/2024 0:15' },
      do: { value: '6.95', unit: 'mg/L', timestamp: '21/10/2024 0:15' },
      ph: { value: '8.05', unit: 'pH', timestamp: '21/10/2024 0:15' },
      temp: { value: '29.80', unit: '°C', timestamp: '21/10/2024 0:15' },
      cond: { value: '59.30', unit: 'mS/cm', timestamp: '21/10/2024 0:15' },
      depth: { value: '0.28', unit: 'm', timestamp: '21/10/2024 0:15' },
      turbidity: { value: '3.45', unit: 'NTU', timestamp: '21/10/2024 0:15' }
    }
  },
  'NB005': {
    id: 'NB005',
    name: 'Sensor 5',
    status: 'online',
    batteryLevel: 95,
    location: { lat: 24.8200, long: 54.7000 },
    readings: {
      salinity: { value: '42.80', unit: 'ppt', timestamp: '21/10/2024 0:15' },
      do: { value: '6.40', unit: 'mg/L', timestamp: '21/10/2024 0:15' },
      ph: { value: '8.30', unit: 'pH', timestamp: '21/10/2024 0:15' },
      temp: { value: '32.10', unit: '°C', timestamp: '21/10/2024 0:15' },
      cond: { value: '63.20', unit: 'mS/cm', timestamp: '21/10/2024 0:15' },
      depth: { value: '0.35', unit: 'm', timestamp: '21/10/2024 0:15' },
      turbidity: { value: '6.20', unit: 'NTU', timestamp: '21/10/2024 0:15' }
    }
  }
};

/**
 * Generate fallback data for any sensor ID
 * This function creates generic sensor data for sensors not in the predefined list
 */
export const generateFallbackSensorData = (sensorId: string): SensorData => {
  return {
    id: sensorId,
    name: `Sensor ${sensorId}`,
    status: 'unknown',
    batteryLevel: 0,
    location: { lat: 24.7010, long: 54.5123 },
    readings: {
      salinity: { value: '0', unit: 'ppt', timestamp: 'No data' },
      do: { value: '0', unit: 'mg/L', timestamp: 'No data' },
      ph: { value: '0', unit: 'pH', timestamp: 'No data' },
      temp: { value: '0', unit: '°C', timestamp: 'No data' },
      cond: { value: '0', unit: 'mS/cm', timestamp: 'No data' },
      depth: { value: '0', unit: 'm', timestamp: 'No data' },
      turbidity: { value: '0', unit: 'NTU', timestamp: 'No data' }
    }
  };
};

// Interface for sensor readings used by the chart components
interface ChartSensorReading {
  value: number;
  timestamp: string;
}

// Interface for signal reading
interface SignalReading {
  signalLevel: number;
  status: 'online' | 'offline' | 'unknown';
  latitude: number;
  longitude: number;
}

/**
 * Gets sensor signal data from local fallback data
 * This is a fallback function when the API fails
 * @param sensorId - ID of the sensor to get signal data for
 * @returns Signal reading or null if not found
 */
export const getSensorSignalData = (sensorId: string): SignalReading | null => {
  try {
    const sensorData = sensorsData[sensorId];
    if (!sensorData) {
      console.warn(`No local signal data found for sensor: ${sensorId}`);
      return null;
    }

    // Generate signal data based on sensor status and location
    const signalLevel = sensorData.status === 'online' ? sensorData.batteryLevel : 0;

    return {
      signalLevel,
      status: sensorData.status,
      latitude: sensorData.location.lat,
      longitude: sensorData.location.long
    };
  } catch (error) {
    console.error(`Error getting local signal data for sensor ${sensorId}:`, error);
    return null;
  }
};

/**
 * Gets sensor readings from local fallback data
 * This is a fallback function when the API fails
 * @param sensorId - ID of the sensor to get readings for
 * @param parameter - Parameter to get readings for (e.g. 'salinity', 'temp')
 * @returns Array of sensor readings
 */
export const getSensorReadings = (sensorId: string, parameter: string): ChartSensorReading[] => {
  try {
    const sensorData = sensorsData[sensorId];
    if (!sensorData) {
      console.warn(`No local readings data found for sensor: ${sensorId}`);
      return [];
    }

    // Map parameter names to sensor data keys
    const parameterMap: { [key: string]: keyof SensorData['readings'] } = {
      salinity: 'salinity',
      do: 'do',
      ph: 'ph',
      temp: 'temp',
      cond: 'cond',
      depth: 'depth',
      turbidity: 'turbidity'
    };

    const paramKey = parameterMap[parameter];
    if (!paramKey) {
      console.warn(`Unknown parameter: ${parameter}`);
      return [];
    }

    const reading = sensorData.readings[paramKey];
    if (!reading || reading.timestamp === 'No data') {
      return [];
    }

    // Return a single reading as an array (since this is fallback data)
    // In a real scenario, this would return historical data
    return [{
      value: parseFloat(reading.value.toString()),
      timestamp: reading.timestamp
    }];
  } catch (error) {
    console.error(`Error getting local readings for sensor ${sensorId}, parameter ${parameter}:`, error);
    return [];
  }
};